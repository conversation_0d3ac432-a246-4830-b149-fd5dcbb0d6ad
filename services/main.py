import logging
import sys
import time
from datetime import datetime, timedelta
from typing import Optional

from fastapi import FastAPI, Request, HTTPException, Query, APIRouter
from pybit.unified_trading import HTTP

"""
🔧 Bybit FastAPI Service Management (on remote server):

# SSH into the server:
ssh root@128.199.252.188

# 🚀 Upload updated main.py from local to server:
scp PycharmProjects/Journal/services/main.py root@128.199.252.188:/home/<USER>/

# 🔁 Restart the FastAPI service:
sudo systemctl restart fastapi-bybit

# ✅ Check service status:
sudo systemctl status fastapi-bybit

# 📄 View logs (tail):
journalctl -u fastapi-bybit -f

# 📄 View last 100 log lines:
journalctl -u fastapi-bybit -n 100
"""

API_TOKEN = "yoursecret123"

app = FastAPI()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)  # send to stdout (journalctl picks this up)
    ]
)

logger = logging.getLogger("bybit")  # optional: name your logger
logger.info("✅ Logging is now set up and should appear in journalctl")


def get_session():
    return HTTP(
        testnet=False,
        api_key="yOLByDEdvH0T6q8T0z",
        api_secret="cWc1Ed9C2cYt6TqR4zcP3VaVTBv18otM8qnR"
    )


@app.middleware("http")
async def verify_token(request: Request, call_next):
    if request.headers.get("x-api-token") != API_TOKEN:
        raise HTTPException(status_code=403, detail="Invalid token")
    return await call_next(request)


@app.get("/debug")
def debug():
    logger.info("✅ This should be in journalctl")
    return {"ok": True}


@app.get("/bybit/balance")
def get_balance():
    result = get_session().get_wallet_balance(accountType="UNIFIED", coin="USDT")
    return {"equity": float(result["result"]["list"][0]["totalEquity"])}


@app.get("/bybit/executions")
def get_executions():
    result = get_session().get_executions(category="linear", limit=30)
    return result["result"]["list"]


@app.get("/bybit/price")
def get_price(ticker: str):
    result = get_session().get_kline(symbol=ticker, interval=1, limit=30)
    return float(result["result"]["list"][0][4])


@app.get("/bybit/orders")
def get_orders(startTime: Optional[int] = None, endTime: Optional[int] = None):
    session = get_session()
    orders_list = []

    # If no startTime provided, default to 2 years ago
    if not startTime:
        start_dt = datetime.utcnow() - timedelta(days=730)
    else:
        start_dt = datetime.utcfromtimestamp(startTime / 1000)

    # If no endTime provided, default to now
    if not endTime:
        end_dt = datetime.utcnow()
    else:
        end_dt = datetime.utcfromtimestamp(endTime / 1000)

    start_ms = int(start_dt.timestamp() * 1000)
    end_ms = int(end_dt.timestamp() * 1000)

    logger.info(f"🔍 Starting order fetch from {start_dt} to {end_dt}")
    logger.info(f"🧮 Start MS: {start_ms}, End MS: {end_ms}")

    while start_ms < end_ms:
        window_end_ms = min(start_ms + (7 * 24 * 60 * 60 * 1000), end_ms)

        logger.info(f"\n📅 Fetching window:")
        logger.info(f"   Start: {datetime.utcfromtimestamp(start_ms / 1000)} ({start_ms})")
        logger.info(f"   End:   {datetime.utcfromtimestamp(window_end_ms / 1000)} ({window_end_ms})")
        delta_ms = window_end_ms - start_ms
        logger.info(f"⏱ Window size: {delta_ms} ms ({delta_ms / 1000:.2f} seconds)")

        nxtPageCur = None
        page_count = 0
        total_in_window = 0

        while True:
            kwargs = {
                "category": "linear",
                "limit": 50,
                "startTime": start_ms,
                "endTime": window_end_ms
            }
            if nxtPageCur:
                kwargs["cursor"] = nxtPageCur

            response = session.get_order_history(**kwargs)
            result = response["result"]
            page_orders = result.get("list", [])
            orders_list += page_orders
            total_in_window += len(page_orders)
            nxtPageCur = result.get("nextPageCursor")
            page_count += 1

            logger.info(f"   🔄 Page {page_count}: {len(page_orders)} orders")

            if not nxtPageCur or page_count > 30:
                break

            time.sleep(0.1)

        logger.info(f"✅ Finished window: {total_in_window} orders across {page_count} page(s)")

        start_ms = window_end_ms + 1  # move forward 7 days

    logger.info(f"\n🎯 Total orders fetched: {len(orders_list)}")
    return orders_list


@app.get("/bybit/positions")
def get_open_positions(category: str = "linear", settleCoin: Optional[str] = None):
    """
    Get open positions from Bybit.

    Args:
        category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
        settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

    Returns:
        list: A list of open positions
    """
    session = get_session()

    # Prepare parameters for the API call
    params = {"category": category}
    if settleCoin:
        params["settleCoin"] = settleCoin

    # Make the API call with the parameters
    response = session.get_positions(**params)
    logger.info(response)
    # Filter out closed positions (size = 0)
    open_positions = [p for p in response["result"]["list"] if float(p["size"]) > 0]

    logger.info(f"Retrieved {len(open_positions)} open positions for category={category}" +
                (f", settleCoin={settleCoin}" if settleCoin else ""))

    return open_positions


@app.get("/bybit/kline")
def bybit_kline(symbol: str, interval: str = "60", limit: int = 500,
                start: int | None = None, end: int | None = None):
    # Use public V5 endpoint directly (no auth) from Japan
    import requests
    url = "https://api.bybit.com/v5/market/kline"
    params = {"category": "linear", "symbol": symbol, "interval": interval, "limit": min(max(1, limit), 1000)}
    if start is not None: params["start"] = int(start)
    if end is not None: params["end"] = int(end)
    r = requests.get(url, params=params, timeout=10)
    r.raise_for_status()
    return r.json()


@app.get("/bybit/instruments")
def bybit_instruments():
    import requests
    url = "https://api.bybit.com/v5/market/instruments-info"
    params = {"category": "linear"}
    r = requests.get(url, params=params, timeout=10)
    r.raise_for_status()
    return r.json()
