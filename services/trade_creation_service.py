"""
Trade Creation Service

Handles the creation of structured Trade and Order objects from various drawing tools
(Fibonacci retracements, rectangles, lines, etc.) for future API execution.
"""

import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any

from models.order import Order, BuySell
from models.trade import Trade, TradeStatus, TradeDirection, Exchange


class OrderType:
    """Order type constants for trade setups"""
    STOP_LOSS = "Stop Loss"
    ENTRY = "Entry"
    TAKE_PROFIT = "Take Profit"
    MARKET = "Market"
    LIMIT = "Limit"
    STOP_LIMIT = "Stop Limit"


class OrderStatus:
    """Order status constants for trade setups"""
    SETUP = "Setup"
    PENDING = "Pending"
    FILLED = "Filled"
    CANCELLED = "Cancelled"


class TradeCreationService:
    """Service for creating structured trades from drawing tool data"""

    @staticmethod
    def create_fibonacci_trade(fib_data: Dict[str, Any], account_size: Decimal = Decimal("10000")) -> Trade:
        """
        Create a Trade object with Orders from Fibonacci retracement data.
        
        Args:
            fib_data: Dictionary containing Fibonacci level data
            account_size: Account size for risk calculations
            
        Returns:
            Trade object with structured orders
        """
        # Extract data from request
        symbol = fib_data.get("symbol", "UNKNOWN")
        exchange_str = fib_data.get("exchange", "binance").upper()
        timeframe = fib_data.get("timeframe", "1h")

        # Apply adaptive precision to all price values
        from models.trade import Trade
        p1 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("p1", 0))))
        p2 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("p2", 0))))
        fib50 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib50", 0))))
        fib618 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib618", 0))))
        fib786 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib786", 0))))
        fibtp236 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fibtp236", 0))))
        fibtp382 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fibtp382", 0))))
        timestamp = fib_data.get("timestamp")

        # Determine trade direction
        direction = TradeDirection.LONG if p1 < p2 else TradeDirection.SHORT

        # Map exchange string to enum
        exchange = Exchange.BINANCE  # Default
        try:
            exchange = Exchange[exchange_str]
        except KeyError:
            pass

        # Create timestamp
        trade_time = datetime.now(timezone.utc)
        if timestamp:
            trade_time = datetime.fromtimestamp(timestamp / 1000, timezone.utc)

        # Generate unique trade ID
        trade_id = str(uuid.uuid4())

        # Create orders list
        orders = []

        # 1. Stop Loss Order (P1)
        stop_loss_order = TradeCreationService._create_order(
            order_id=f"{trade_id}_SL",
            symbol=symbol,
            order_type=OrderType.STOP_LOSS,
            price=p1,
            quantity=Decimal("6"),  # Total position size
            buy_sell=BuySell.SELL if direction == TradeDirection.LONG else BuySell.BUY,
            created_date=trade_time,
            notes="Stop Loss at P1"
        )
        orders.append(stop_loss_order)

        # 2. Entry Orders (50%, 61.8%, 78.6% retracements)
        entry_levels = [
            {"level": "50%", "price": fib50, "qty": Decimal("2")},
            {"level": "61.8%", "price": fib618, "qty": Decimal("2")},
            {"level": "78.6%", "price": fib786, "qty": Decimal("2")}
        ]

        for i, entry in enumerate(entry_levels):
            entry_order = TradeCreationService._create_order(
                order_id=f"{trade_id}_ENTRY_{i + 1}",
                symbol=symbol,
                order_type=OrderType.ENTRY,
                price=entry["price"],
                quantity=entry["qty"],
                buy_sell=BuySell.BUY if direction == TradeDirection.LONG else BuySell.SELL,
                created_date=trade_time,
                notes=f"Entry at {entry['level']} Fibonacci level"
            )
            orders.append(entry_order)

        # 3. Take Profit Orders (23.6% and 38.2% extensions)
        tp_levels = [
            {"level": "23.6%", "price": fibtp236, "qty": Decimal("3"), "note": "Half position"},
            {"level": "38.2%", "price": fibtp382, "qty": Decimal("3"), "note": "Remaining position"}
        ]

        for i, tp in enumerate(tp_levels):
            tp_order = TradeCreationService._create_order(
                order_id=f"{trade_id}_TP_{i + 1}",
                symbol=symbol,
                order_type=OrderType.TAKE_PROFIT,
                price=tp["price"],
                quantity=tp["qty"],
                buy_sell=BuySell.SELL if direction == TradeDirection.LONG else BuySell.BUY,
                created_date=trade_time,
                notes=f"Take Profit at {tp['level']} - {tp['note']}"
            )
            orders.append(tp_order)

        # # Calculate trade metrics with adaptive precision
        # total_quantity = sum(order.quantity for order in orders if order.orderType == OrderType.ENTRY)
        # avg_entry_price = Trade.apply_adaptive_precision(
        #     sum(order.price * order.quantity for order in orders if order.orderType == OrderType.ENTRY) / total_quantity
        # )
        # risk_amount = Trade.apply_adaptive_precision(abs(avg_entry_price - p1) * total_quantity)
        # risk_percent = Trade.apply_adaptive_precision((risk_amount / account_size) * Decimal("100"))
        # notional = Trade.apply_adaptive_precision(avg_entry_price * total_quantity)

        # Create Trade object
        trade = Trade(
            id_field=None,  # Will be set when saved to database
            exchange_trade_id=trade_id,
            trade_orders=orders,
            unfilled_orders=[],
            symbol=symbol,
            accountBalance=account_size,
            exchange=exchange,
            direction=direction,
            timeOpen=trade_time,
            status=TradeStatus.SETUP,
            tradeQty=None,
            openQty=None,  # No position opened yet
            lastUpdate=trade_time,
            timeClose=None,
            duration=None,
            chartLink=None,
            notes=f"Fibonacci retracement trade setup - {direction.value}",
            notional=None,
            leverage=None,
            avgOpenPrice=None,
            avgClosePrice=None,
            riskAmt=None,
            riskPercent=None,
            profit=Decimal("0"),
            fees=Decimal("0"),
            strategy="Fibonacci Retracement",
            time_frame=timeframe,
            username="system"
        )
        trade.update_trade_details()
        trade.status = TradeStatus.SETUP
        return trade

    @staticmethod
    def _create_order(order_id: str, symbol: str, order_type: str, price: Decimal,
                      quantity: Decimal, buy_sell: BuySell, created_date: datetime,
                      notes: str = None) -> Order:
        """Create a standardized Order object for trade setups"""
        from models.trade import Trade

        return Order(
            id_field=None,  # Will be set when saved to database
            order_id=order_id,
            trade_id=None,  # Will be set when associated with trade
            created_date=created_date,
            filled_date=None,
            symbol=symbol,
            orderType=order_type,
            orderStatus=OrderStatus.SETUP,
            buySell=buy_sell,
            reduce=order_type == OrderType.STOP_LOSS or order_type == OrderType.TAKE_PROFIT,
            price=Trade.apply_adaptive_precision(price),  # Apply adaptive precision to price
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=quantity,
            filledQuantity=Decimal("0"),
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )

    @staticmethod
    def create_rectangle_trade(rect_data: Dict[str, Any], account_size: Decimal = Decimal("10000")) -> Trade:
        """
        Create a Trade object from rectangle drawing data.
        Future implementation for rectangle-based trades.
        """
        # Placeholder for future rectangle trade implementation
        raise NotImplementedError("Rectangle trade creation not yet implemented")

    @staticmethod
    def create_line_trade(line_data: Dict[str, Any], account_size: Decimal = Decimal("10000")) -> Trade:
        """
        Create a Trade object from line drawing data.
        Future implementation for line-based trades.
        """
        # Placeholder for future line trade implementation
        raise NotImplementedError("Line trade creation not yet implemented")
