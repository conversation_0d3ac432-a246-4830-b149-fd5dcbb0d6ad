import logging
import traceback
from decimal import ROUND_HALF_UP

import requests

# Configure logger for Bybit API requests
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Other imports are done within methods to avoid circular imports
# Timeframe mapping for Bybit V5 intervals
_INTERVAL_MAP = {
    "1m": "1", "3m": "3", "5m": "5", "15m": "15", "30m": "30",
    "1h": "60", "2h": "120", "4h": "240", "6h": "360",
    # 8h not supported, skip
    "12h": "720", "1d": "D", "1w": "W", "1M": "M",
}


class Bybit:
    API_URL = "http://***************:8000"
    API_TOKEN = "yoursecret123"
    HEADERS = {"x-api-token": API_TOKEN}

    @staticmethod
    def _log_api_request(method, url, params=None, headers=None, response=None, error=None):
        """
        Log API request details for debugging and monitoring.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Full URL of the request
            params: Request parameters
            headers: Request headers (sensitive data will be masked)
            response: Response object or data
            error: Exception if request failed
        """
        # Mask sensitive headers for logging
        safe_headers = {}
        if headers:
            for key, value in headers.items():
                if 'token' in key.lower() or 'auth' in key.lower():
                    safe_headers[key] = "***MASKED***"
                else:
                    safe_headers[key] = value

        log_data = {
            "method": method,
            "url": url,
            "params": params,
            "headers": safe_headers
        }

        if error:
            logger.error(f"Bybit API request failed: {log_data}, Error: {error}")
        else:
            status_code = getattr(response, 'status_code', 'N/A')
            response_size = len(str(response.text)) if hasattr(response, 'text') else 'N/A'
            logger.info(f"Bybit API request: {log_data}, Status: {status_code}, Response size: {response_size} chars")

    @staticmethod
    def getSession():
        raise NotImplementedError("Remote proxy does not use getSession().")

    @staticmethod
    def getExecutions():
        url = f"{Bybit.API_URL}/bybit/executions"
        try:
            r = requests.get(url, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()  # Raise an exception for bad status codes
            return r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def getPrice(ticker):
        url = f"{Bybit.API_URL}/bybit/price"
        params = {"ticker": ticker}
        try:
            r = requests.get(url, params=params, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            return float(r.json())
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def get_current_open_positions(category: str = "linear", settleCoin: str = None, username: str = None):
        """
        Retrieves open positions from Bybit and creates Trade objects
        representing the current open positions.

        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

        Returns:
            list: A list of Trade objects representing open positions
            :param username:
        """
        from models.bybit_position import BybitPosition
        from models.trade import Trade

        # Prepare parameters for the API call
        params = {"category": category}
        if settleCoin:
            params["settleCoin"] = settleCoin

        # Get positions data from API
        url = f"{Bybit.API_URL}/bybit/positions"
        try:
            r = requests.get(url, params=params, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            positions_data = r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, error=e)
            raise

        # Filter and convert to BybitPosition objects
        # Check for both "qty" and "size" fields to handle different API response formats
        positions = []
        for p in positions_data:
            # Check if position has size/qty > 0
            position_size = float(p.get("size", p.get("qty", 0)))
            if position_size > 0:
                try:
                    position = BybitPosition(p)
                    positions.append(position)
                except Exception as e:
                    traceback.print_exc()
                    print(f"Error creating BybitPosition from data: {e}")
                    print(f"Position data: {p}")
                    # Continue processing other positions even if one fails

        # Convert positions directly to trades
        trades = []
        for position in positions:
            try:
                # Create a Trade object directly from Bybit Position since we don't have opening order data
                trade = Trade.from_bybit_position(position, username=username)
                trades.append(trade)
            except Exception as e:
                # import logging
                traceback.print_exc()
                print(f"Error processing position {position.symbol}: {e}")
                # Continue processing other positions even if one fails

        # Log summary
        print(f"Processed {len(positions)} positions into {len(trades)} trades")
        return trades

    @staticmethod
    def getOrders(start_ms=None, end_ms=None):
        from models.bybit_order import BybitOrder
        params = {}
        if start_ms: params["startTime"] = int(start_ms)
        if end_ms: params["endTime"] = int(end_ms)

        url = f"{Bybit.API_URL}/bybit/orders"
        try:
            r = requests.get(url, params=params, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            orders_list = [BybitOrder(order) for order in r.json()]
            return orders_list
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def getAccountBalance():
        """
        Gets the account balance from Bybit as a formatted string.

        Returns:
            str: Formatted account balance
        """

    @staticmethod
    def get_symbols():
        """Return list of Bybit linear (USDT) symbols via Japan proxy."""
        url = f"{Bybit.API_URL}/bybit/instruments"
        try:
            r = requests.get(url, headers=Bybit.HEADERS, timeout=10)
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            data = r.json()
            items = (data.get("result", {}).get("list", []) or [])
            return [it.get("symbol") for it in items if it.get("symbol")]
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def get_candlestick_dataframe(symbol: str, timeframe: str = "1h",
                                  limit: int = 500, start_ms: int | None = None, end_ms: int | None = None):
        """Fetch candles through Japan proxy and return a pandas DataFrame indexed by open_time (UTC)."""
        import pandas as pd
        interval = _INTERVAL_MAP[timeframe]
        url = f"{Bybit.API_URL}/bybit/kline"
        params = {"symbol": symbol.upper(), "interval": interval, "limit": min(max(1, limit), 1000)}
        if start_ms is not None: params["start"] = int(start_ms)
        if end_ms is not None: params["end"] = int(end_ms)
        try:
            r = requests.get(url, params=params, headers=Bybit.HEADERS, timeout=10)
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            payload = r.json()
            rows = list(reversed((payload.get("result", {}) or {}).get("list", []) or []))
            # interval ms
            step_ms = {
                          "1": 60, "3": 180, "5": 300, "15": 900, "30": 1800,
                          "60": 3600, "120": 7200, "240": 14400, "360": 21600,
                          "720": 43200, "D": 86400, "W": 604800, "M": 2592000
                      }[interval] * 1000
            recs = []
            for it in rows:  # [startMs, open, high, low, close, volume, turnover]
                ts_ms = int(it[0])
                recs.append({
                    "open_time": pd.to_datetime(ts_ms, unit="ms", utc=True),
                    "open": float(it[1]), "high": float(it[2]),
                    "low": float(it[3]), "close": float(it[4]),
                    "volume": float(it[5]) if len(it) > 5 and it[5] is not None else 0.0,
                    "close_time": pd.to_datetime(ts_ms + step_ms, unit="ms", utc=True),
                })
            df = pd.DataFrame.from_records(recs)
            if not df.empty:
                df.set_index("open_time", inplace=True)
            return df
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=Bybit.HEADERS, error=e)
            raise

        url = f"{Bybit.API_URL}/bybit/balance"
        try:
            r = requests.get(url, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            return f"{float(r.json()['equity']):.4f}"
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def getAccountBalanceDecimal():
        """
        Gets the account balance from Bybit as a Decimal value.

        Returns:
            Decimal: Account balance as a Decimal
        """
        from decimal import Decimal
        url = f"{Bybit.API_URL}/bybit/balance"
        try:
            r = requests.get(url, headers=Bybit.HEADERS)
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, response=r)
            r.raise_for_status()
            balance = Decimal(str(r.json()['equity']))
            return balance.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=Bybit.HEADERS, error=e)
            raise

    @staticmethod
    def get_bybit_orders(category: str = "linear", settleCoin: str = None, username: str = None):
        """
        Retrieves orders from Bybit and current open positions.

        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

        Returns:
            tuple: (current_open_positions, orders, first_import) - Lists of current open positions and orders,
            and a boolean indicating if it's the first import
        """
        from datetime import datetime, timedelta
        import time
        from models.order import Order
        from models.trade import Exchange
        from trades_db import TradesDB
        import helper

        last_update = helper.date_to_ms(TradesDB.get_bybit_last_order_time(), 1000)
        current_open_positions = []
        if last_update:
            now_ms = int(time.time() * 1000)
            orders = Bybit.getOrders(start_ms=last_update + 1, end_ms=now_ms)

            # For non-first imports, we get open positions from the DB since we recreate oldest - newest now
            current_open_positions = TradesDB.get_open_trades_by_exchange(Exchange.BYBIT)
            # Sort current_open_positions by oldest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=False)
        else:
            # 2 years ago in milliseconds
            two_years_ago = datetime.utcnow() - timedelta(days=60)  # 365 * 2
            two_years_ms = int(two_years_ago.timestamp() * 1000)
            now_ms = int(time.time() * 1000)

            start_dt = datetime.utcfromtimestamp(two_years_ms / 1000)
            end_dt = datetime.utcfromtimestamp(now_ms / 1000)

            start_ms = int(start_dt.timestamp() * 1000)
            end_ms = int(end_dt.timestamp() * 1000)

            # Calculates the first 7 day chunk
            window_end_ms = min(start_ms + (7 * 24 * 60 * 60 * 1000), end_ms)
            Bybit.print_time_diff(start_ms, window_end_ms)
            orders = Bybit.getOrders(start_ms=start_ms, end_ms=now_ms)

            # For first import scenario we get live open positions, no open positions in DB (duh), also
            # Re-create trades first run is newest - oldest
            current_open_positions = Bybit.get_current_open_positions(category=category,
                                                                      settleCoin=settleCoin,
                                                                      username=username)

            # Sort current_open_positions by newest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=True)

        # Get the set of already processed order IDs
        processed_order_ids = set(TradesDB.getBybitOrderIds())

        # Filter out orders that have already been processed
        orders = [order for order in orders if order.bybit_order_id not in processed_order_ids]

        # Convert and return the new orders
        orders = [Order.from_bybit_order(order) for order in orders]

        # Sort orders chronologically by filled_date (or created_date if filled_date is None)
        # This ensures we process the oldest orders first for proper trade reconstruction
        def get_order_date(order):
            return order.filled_date if order.filled_date else order.created_date

        # TODO: last_update is not none for new orders?
        if last_update:
            sorted_orders = sorted(orders, key=get_order_date, reverse=False)
        else:
            sorted_orders = sorted(orders, key=get_order_date, reverse=True)

        return current_open_positions, sorted_orders, True if last_update is None else False

    @staticmethod
    def print_time_diff(start_ms, end_ms):
        """
        Prints the time difference between two timestamps in a human-readable format.

        Args:
            start_ms: Start time in milliseconds
            end_ms: End time in milliseconds
        """
        diff_ms = end_ms - start_ms

        days = diff_ms // (24 * 60 * 60 * 1000)
        diff_ms %= (24 * 60 * 60 * 1000)

        hours = diff_ms // (60 * 60 * 1000)
        diff_ms %= (60 * 60 * 1000)

        minutes = diff_ms // (60 * 1000)
        diff_ms %= (60 * 1000)

        seconds = diff_ms // 1000
        ms = diff_ms % 1000

        print(f"Difference: {days}d {hours}h {minutes}m {seconds}s {ms}ms")
