from __future__ import annotations

"""
🔧 Chart API Server Management:

# 🚀 Start the server:
uvicorn services.chart_api:app --host 0.0.0.0 --port 8900

# 🔄 Start with auto-reload (recommended for development):
uvicorn services.chart_api:app --host 0.0.0.0 --port 8900 --reload

# 🛑 Stop the server:
Press Ctrl+C in the terminal where it's running

# 🔍 Check if server is running:
curl "http://localhost:8900/candles?exchange=binance&symbol=BTCUSDT&timeframe=1h&limit=1"
Browser: "http://localhost:8900/candles/historical?exchange=binance&symbol=BTCUSDT&timeframe=1h&before=1753153200000&limit=10"

# 📊 Available endpoints:
- GET /candles?exchange=binance&symbol=BTCUSDT&timeframe=1h&limit=1000
- GET /candles/historical?exchange=binance&symbol=BTCUSDT&timeframe=1h&before=1640995200000&limit=500
- GET /symbols?exchange=binance

# 📝 Notes:
- Server must be running for charting interface to load market data
- Uses Binance US API (no geo-blocking issues)
- Serves data to charting.html via localhost:8900
"""

from fastapi import FastAPI, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from services.candle_service import CandleService

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

svc = CandleService()


@app.get("/candles")
def candles(exchange: str = "binance", symbol: str = "BTCUSDT", timeframe: str = "1h",
            limit: int = 1000, start: int | None = None, end: int | None = None):
    try:
        print(f"[CHART_API] GET /candles - exchange={exchange}, symbol={symbol}, timeframe={timeframe}, limit={limit}")
        df = svc.get_candles(exchange, symbol, timeframe, limit, start, end)
        if df is None or df.empty:
            print(f"[CHART_API] Empty dataframe returned for {exchange}/{symbol}/{timeframe}")
            return []
        print(f"[CHART_API] Returning {len(df)} candles for {exchange}/{symbol}/{timeframe}")
        return [
            {
                "time": int(ts.timestamp()),
                "open": float(r["open"]),
                "high": float(r["high"]),
                "low": float(r["low"]),
                "close": float(r["close"]),
                "volume": float(r.get("volume", 0.0)),
            }
            for ts, r in df.iterrows()
        ]
    except Exception as e:
        import traceback
        print(f"[CHART_API] ERROR in /candles - exchange={exchange}, symbol={symbol}, timeframe={timeframe}")
        print(f"[CHART_API] Exception: {type(e).__name__}: {str(e)}")
        print(f"[CHART_API] Traceback:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/candles/historical")
def candles_hist(exchange: str = "binance", symbol: str = "BTCUSDT", timeframe: str = "1h",
                 before: int = Query(...), limit: int = 1000):
    print(f"[CHART_API] GET /candles/historical - exchange={exchange}, symbol={symbol}, timeframe={timeframe}, before={before}, limit={limit}")
    return candles(exchange=exchange, symbol=symbol, timeframe=timeframe, limit=limit, start=None, end=before)


@app.get("/symbols")
def symbols(exchange: str = "binance"):
    try:
        print(f"[CHART_API] GET /symbols - exchange={exchange}")
        result = svc.get_symbols(exchange)
        print(f"[CHART_API] Returning {len(result) if result else 0} symbols for {exchange}")
        return result
    except Exception as e:
        import traceback
        print(f"[CHART_API] ERROR in /symbols - exchange={exchange}")
        print(f"[CHART_API] Exception: {type(e).__name__}: {str(e)}")
        print(f"[CHART_API] Traceback:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


# -------------------------------
# Trade Endpoints
# -------------------------------
from pydantic import BaseModel
from typing import Optional
import uuid


class FibonacciTradeRequest(BaseModel):
    exchange: str = "binance"
    symbol: str
    timeframe: str = "1h"
    p1: float
    p2: float
    fib50: float
    fib618: float
    fib786: float
    fibtp236: float
    fibtp382: float
    timestamp: Optional[int] = None  # ms since epoch


@app.post("/trades/fibonacci")
def create_fibonacci_trade(req: FibonacciTradeRequest):
    """Accept a Fibonacci-based trade setup from charting.html.

    For now, we simply acknowledge receipt and echo back a computed plan
    (direction, stop, entries, take profits). Persistence will be added later.
    """
    try:
        direction = "LONG" if req.p1 < req.p2 else "SHORT"
        stop_loss = req.p1  # for both long/short per current UI semantics
        entries = [
            {"level": "50%", "price": req.fib50, "qty": 2},
            {"level": "61.8%", "price": req.fib618, "qty": 2},
            {"level": "78.6%", "price": req.fib786, "qty": 2},
        ]
        take_profits = [
            {"level": "23.6%", "price": req.fibtp236, "qty": 3},
            {"level": "38.2%", "price": req.fibtp382, "qty": 3},
        ]

        setup_id = str(uuid.uuid4())
        print(f"[CHART_API] POST /trades/fibonacci -> {req.exchange}/{req.symbol}/{req.timeframe} id={setup_id}")

        return {
            "ok": True,
            "id": setup_id,
            "direction": direction,
            "exchange": req.exchange,
            "symbol": req.symbol,
            "timeframe": req.timeframe,
            "timestamp": req.timestamp,
            "stop_loss": stop_loss,
            "entries": entries,
            "take_profits": take_profits,
            "received": req.dict(),
        }
    except Exception as e:
        import traceback
        print(f"[CHART_API] ERROR in /trades/fibonacci: {type(e).__name__}: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
