// Chart initialization - extracted from GOOD CHART.html
// This file contains the chart setup, data generation, and basic configuration

// ----- <PERSON><PERSON> guard to avoid duplicates on reload -----
if (window.__lw_chart__) {
  try { window.__lw_chart__.remove(); } catch(e){}
  const chartEl0 = document.getElementById('chart');
  while (chartEl0.firstChild) chartEl0.removeChild(chartEl0.firstChild);
}

// ----- Elements -----
const chartEl = document.getElementById('chart');
const listEl  = document.getElementById('list');
const btnNormal = document.getElementById('mode-normal');
const btnLine = document.getElementById('mode-line');
const btnTrendline = document.getElementById('mode-trendline');
const btnFib  = document.getElementById('mode-fib');
const btnRect = document.getElementById('mode-rect');
const btnAutoFib = document.getElementById('auto-fib');
const btnClear= document.getElementById('clear');
const btnDrawingsToggle = document.getElementById('drawings-toggle');
const drawingsPanel = document.getElementById('drawings-panel');
const tradeInfoEl = document.getElementById('trade-info');

// ----- Create chart -----
const chart = window.__lw_chart__ = LightweightCharts.createChart(chartEl, {
  layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
  rightPriceScale: {
    autoScale: true,
    borderVisible: true,
  },
  timeScale: { borderVisible: false, timeVisible: true, secondsVisible: false },
  grid: { horzLines: { color: 'transparent' }, vertLines: { color: 'transparent' } },
  crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
});

// Ensure the chart fills container on mount
(function initSize(){
  const ro = new ResizeObserver(entries => {
    const { width, height } = entries[0].contentRect;
    chart.applyOptions({ width, height });
  });
  ro.observe(chartEl);
})();

// Toggle chart interactions (pan/zoom) while drawing
function setPanZoomEnabled(enabled) {
  chart.applyOptions({
    handleScroll: enabled,  // disable pressed-mouse pan
    handleScale: enabled,   // disable wheel/pinch zoom
  });

  // Optional: change cursor to signal drawing mode
  chartEl.style.cursor = enabled ? "default" : "crosshair";
}

// ----- Right-click Reset View menu -----
console.log('Setting up context menu...');
setTimeout(() => {
  const chartArea = document.querySelector('.chart-area');
  const chartDiv = document.getElementById('chart');
  console.log('chartArea:', chartArea, 'chartDiv:', chartDiv);

  if (!chartArea) {
    console.error('No .chart-area found');
    return;
  }

  function createMenu() {
    let menu = document.getElementById('chart-context-menu');
    if (!menu) {
      menu = document.createElement('div');
      menu.id = 'chart-context-menu';
      menu.style.cssText = 'position:absolute;display:none;min-width:150px;background:#1a1f2e;border:1px solid #2b3340;border-radius:6px;box-shadow:0 6px 20px rgba(0,0,0,.4);z-index:1002;';

      const item = document.createElement('div');
      item.style.cssText = 'padding:8px 12px;color:#c9d1d9;cursor:pointer;font-size:12px;';
      item.textContent = 'Reset View';
      item.onmouseover = () => item.style.background = '#222833';
      item.onmouseout = () => item.style.background = '';
      item.onclick = () => {
        menu.style.display = 'none';
        try { chart.timeScale().fitContent(); } catch(e){ console.warn(e); }
      };

      menu.appendChild(item);
      document.body.appendChild(menu);
    }
    return menu;
  }

  function showMenu(x, y) {
    console.log('showMenu called at', x, y);
    const menu = createMenu();
    menu.style.display = 'block';
    menu.style.left = x + 'px';
    menu.style.top = y + 'px';
  }

  // Try multiple approaches
  document.addEventListener('contextmenu', (e) => {
    console.log('Document contextmenu:', e.target);
    if (chartArea.contains(e.target)) {
      console.log('Inside chart area, preventing default');
      e.preventDefault();
      e.stopPropagation();
      showMenu(e.clientX, e.clientY);
    }
  }, true);

  // Hide menu on click
  document.addEventListener('click', () => {
    const menu = document.getElementById('chart-context-menu');
    if (menu) menu.style.display = 'none';
  });

}, 100);


const series = chart.addCandlestickSeries({
  upColor: '#26a69a', downColor: '#ef5350',
  wickUpColor: '#26a69a', wickDownColor: '#ef5350',
  borderVisible: false,
  priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
});

// Initial series has no data; data will be loaded by symbol-timeframe.js
// Optional: a transparent baseline trendline can be created after real data loads.
