// Symbol and Timeframe management
// This file handles symbol search, timeframe selection, and chart type switching

// ----- Elements -----
const symbolInput = document.getElementById('symbol-input');
const timeframeBtns = document.querySelectorAll('.timeframe-btn');
const lineChartBtn = document.getElementById('line-chart-btn');
const candleChartBtn = document.getElementById('candle-chart-btn');

// ----- Current State -----
let currentSymbol = 'BTCUSDT';
let currentTimeframe = '1h';
let currentExchange = 'binance';
let currentChartType = 'candlestick'; // 'candlestick' or 'line'

let chartPrimed = false;

// API config from charting.html (can be overridden)
const API_BASE = window.CHART_API_BASE || '';
const API_TOKEN = window.CHART_API_TOKEN || '';
const apiFetch = async (path) => {
  try {
    const resp = await fetch(`${API_BASE}${path}`, { headers: API_TOKEN ? { 'x-api-token': API_TOKEN } : {} });
    if (!resp.ok) {
      const body = await resp.text().catch(() => '');
      throw new Error(`HTTP ${resp.status} ${resp.statusText}${body ? ` - ${body.substring(0, 200)}` : ''}`);
    }
    return resp;
  } catch (err) {
    showError(`Chart API request failed. Is the server running at ${API_BASE || 'http://localhost:8900'}?` + (err?.message ? `\n${err.message}` : ''));
    throw err;
  }
};

// ----- Error banner UI -----
function showError(message) {
  let el = document.getElementById('chart-error');
  if (!el) {
    el = document.createElement('div');
    el.id = 'chart-error';
    el.style.position = 'absolute';
    el.style.top = '8px';
    el.style.left = '50%';
    el.style.transform = 'translateX(-50%)';
    el.style.background = '#2b1020';
    el.style.border = '1px solid #b34d5d';
    el.style.color = '#ffd8df';
    el.style.padding = '10px 32px 10px 12px'; // extra right padding for close button
    el.style.borderRadius = '6px';
    el.style.fontSize = '12px';
    el.style.zIndex = '1001';
    el.style.maxWidth = '90%';
    el.style.whiteSpace = 'pre-wrap';
    el.style.boxShadow = '0 2px 8px rgba(0,0,0,0.4)';
    el.style.pointerEvents = 'auto';
    document.querySelector('.chart-area')?.appendChild(el);
  }
  // Set message text
  el.innerText = message;

  // Ensure a single close button, positioned top-right inside the banner
  let close = el.querySelector('.chart-error-close');
  if (!close) {
    close = document.createElement('button');
    close.className = 'chart-error-close';
    close.setAttribute('aria-label','Dismiss');
    close.textContent = '✕';
    close.style.position = 'absolute';
    close.style.top = '6px';
    close.style.right = '8px';
    close.style.background = 'transparent';
    close.style.border = 'none';
    close.style.color = '#ffd8df';
    close.style.cursor = 'pointer';
    close.style.fontSize = '12px';
    close.onclick = () => el.remove();
    el.appendChild(close);
  }
}


// ----- Baseline trendline (prevents pan from hijacking drawing in future space) -----
function ensureBaselineTrendline({ lastPrice, lastTs, candleInterval, numWhiteSpaces }) {
  try {
    const baselinePrice = lastPrice;
    const maxWhitespaceTime = lastTs + (candleInterval * numWhiteSpaces);

    if (!window.baselineTrendlineSeries) {
      window.baselineTrendlineSeries = chart.addLineSeries({
        color: 'transparent',
        lineWidth: 1,
        priceLineVisible: false,
        lastValueVisible: false,
        crosshairMarkerVisible: false,
        title: ''
      });
    }

    window.baselineTrendlineSeries.setData([
      { time: lastTs, value: baselinePrice },
      { time: maxWhitespaceTime, value: baselinePrice }
    ]);
  } catch (e) {
    console.warn('Failed to update baseline trendline:', e);
  }
}

function clearError(){
  const el = document.getElementById('chart-error');
  if (el) el.remove();
}


// ----- Loading overlay helpers -----
const loadingEl = document.getElementById('loading-overlay');
const showLoading = () => { if (loadingEl) loadingEl.classList.add('show'); };
const hideLoading = () => { if (loadingEl) loadingEl.classList.remove('show'); };


// ----- Symbol Input Handler -----
symbolInput.addEventListener('input', (e) => {
  currentSymbol = e.target.value.toUpperCase();
  // TODO: Implement symbol validation and data fetching
  console.log('Symbol changed to:', currentSymbol);
});

symbolInput.addEventListener('keypress', (e) => {
  if (e.key === 'Enter') {
    loadSymbolData(currentSymbol, currentTimeframe, currentExchange);
  }
});

// ----- Timeframe Button Handlers -----
timeframeBtns.forEach(btn => {
  btn.addEventListener('click', () => {
    // Remove active class from all buttons
    timeframeBtns.forEach(b => b.classList.remove('active'));

    // Add active class to clicked button
    btn.classList.add('active');

    // Update current timeframe
    currentTimeframe = btn.dataset.timeframe;

    console.log('Timeframe changed to:', currentTimeframe);
    loadSymbolData(currentSymbol, currentTimeframe, currentExchange);
    // Reset paging and priming when timeframe changes
    noMoreHistory = false;
    lastHistoryBefore = null;
    chartPrimed = false;

  });
});

// ----- Chart Type Button Handlers -----
lineChartBtn.addEventListener('click', () => {
  if (currentChartType !== 'line') {
    currentChartType = 'line';
    lineChartBtn.classList.add('active');
    candleChartBtn.classList.remove('active');

    console.log('Chart type changed to: line');
    switchChartType('line');
  }
});

candleChartBtn.addEventListener('click', () => {
  if (currentChartType !== 'candlestick') {
    currentChartType = 'candlestick';
    candleChartBtn.classList.add('active');
    lineChartBtn.classList.remove('active');
    console.log('Chart type changed to: candlestick');
    switchChartType('candlestick');
  }
});

// ----- Exchange Selector -----
const exchangeSelect = document.getElementById('exchange-select');
if (exchangeSelect) {
  // initialize currentExchange from dropdown on load
  currentExchange = exchangeSelect.value || currentExchange;
  exchangeSelect.addEventListener('change', () => {
    currentExchange = exchangeSelect.value;
    console.log('Exchange changed to:', currentExchange);
    // reset paging flags when switching exchanges
    noMoreHistory = false;
    lastHistoryBefore = null;
    chartPrimed = false;
    // clear any existing error banners
    clearError();
    // reload with new exchange
    loadSymbolData(currentSymbol, currentTimeframe, currentExchange);
  });
}


// ----- Data Loading Function -----
async function loadSymbolData(symbol, timeframe, exchange = currentExchange) {
  showLoading();
  console.log(`Loading data for ${symbol} on ${timeframe} timeframe via ${exchange}`);
  try {
    clearError();
    const resp = await apiFetch(`/candles?exchange=${encodeURIComponent(exchange)}&symbol=${encodeURIComponent(symbol)}&timeframe=${encodeURIComponent(timeframe)}&limit=1000`);
    const data = await resp.json().catch(()=>{ throw new Error('Invalid JSON in response'); });
    if (!Array.isArray(data) || data.length === 0) {
      showError('No candles returned from Chart API.');
      return;
    }

    // Store current data globally and append whitespace points for drawing area
    const candleInterval = data.length > 1 ? (data[1].time - data[0].time) : 3600;
    const lastTs = data[data.length - 1].time;
    const numWhiteSpaces = 200;
    const withWhitespace = [...data];
    for (let i = 1; i <= numWhiteSpaces; i++) {
      withWhitespace.push({ time: lastTs + (candleInterval * i) });
    }
    window.currentData = withWhitespace;

    if (currentChartType === 'line') {
      const lineData = data.map(c => ({ time: c.time, value: c.close }));
      series.setData(lineData);
    } else {
      series.setData(withWhitespace);
    }

    // Recreate baseline trendline using real data context
    const lastPrice = data[data.length - 1].close;
    ensureBaselineTrendline({ lastPrice, lastTs, candleInterval, numWhiteSpaces });

    // Set visible range to show some future space
    const futureTime = lastTs + (candleInterval * 7);
    chart.timeScale().setVisibleRange({
      from: data[Math.max(0, data.length - numWhiteSpaces - 100)].time,
      to: futureTime
    });

    // Prime chart once with a full reset to ensure price scale/candles render
    if (!chartPrimed) {
      try {
        chart.timeScale().fitContent();
        // force small async to let layout settle, then recalc scales
        setTimeout(() => {
          try {
            chart.timeScale().fitContent();
            // Trigger price scale recalculation by toggling autoscale
            chart.applyOptions({ rightPriceScale: { autoScale: true } });
            chart.applyOptions({ rightPriceScale: { autoScale: false } });
          } catch (e) { /* noop */ }
        }, 0);
      } finally {
        chartPrimed = true;
      }
    }
    // After data applied, align the last real bar to the right edge (no overshoot)
    chart.timeScale().scrollToPosition(0, false);

  } catch (e) {
    console.error('Error loading candles:', e);
  } finally {
    hideLoading();
  }
}


// ----- Backward Paging on Visible Range -----
let isLoadingHistory = false;
let noMoreHistory = false;
let lastHistoryBefore = null;
chart.timeScale().subscribeVisibleTimeRangeChange(async (range) => {
  if (!range || !Array.isArray(window.currentData) || window.currentData.length === 0) return;
  if (noMoreHistory) return;

  const earliest = window.currentData.find(d => d && d.time)?.time;
  if (!earliest) return;

  const span = range.to - range.from;
  const leftEdgeBuffer = span * 0.1; // 10% of viewport width
  const distanceFromEarliest = range.from - earliest;

  // Only fetch when the user scrolls close to the left edge and not already loading
  if (distanceFromEarliest <= leftEdgeBuffer && !isLoadingHistory) {
    const beforeMs = earliest * 1000;
    if (beforeMs === lastHistoryBefore) return; // prevent duplicate same-page fetch loops
    isLoadingHistory = true;
    lastHistoryBefore = beforeMs;
    try {
      const resp = await apiFetch(`/candles/historical?exchange=${encodeURIComponent(currentExchange)}&symbol=${encodeURIComponent(currentSymbol)}&timeframe=${encodeURIComponent(currentTimeframe)}&before=${beforeMs}&limit=500`);
      const older = await resp.json();
      if (Array.isArray(older) && older.length > 0) {
        // Prepend unique older candles
        const existingTimes = new Set(window.currentData.filter(d => d && d.time).map(d => d.time));
        const prepend = older.filter(o => !existingTimes.has(o.time));
        if (prepend.length > 0) {
          window.currentData = [...prepend, ...window.currentData];
          if (currentChartType === 'line') {
            const lineData = window.currentData.filter(d => d && d.close).map(c => ({ time: c.time, value: c.close }));
            series.setData(lineData);
          } else {
            series.setData(window.currentData);
          }
        } else {
          // No new data; stop further paging
          noMoreHistory = true;
        }
      } else {
        // Server returned empty page; stop trying
        noMoreHistory = true;
      }
    } catch (err) {
      console.warn('Historical fetch failed:', err);
    } finally {
      isLoadingHistory = false;
    }
  }
});

// ----- Chart Type Switching -----
function switchChartType(type) {
  if (!chart || !series) {
    console.error('Chart or series not initialized');
    return;
  }

  try {
    // Remove current series
    chart.removeSeries(series);

    // Create new series based on type
    if (type === 'line') {
      window.series = chart.addLineSeries({
        color: '#58a6ff',
        lineWidth: 2,
        priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
      });

      // Convert candlestick data to line data (using close prices)
      const lineData = window.currentData ? window.currentData.map(candle => ({
        time: candle.time,
        value: candle.close
      })) : [];

      series.setData(lineData);
    } else {
      window.series = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
        borderVisible: false,
        priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
      });

      // Use stored candlestick data
      if (window.currentData) {
        series.setData(window.currentData);
        // Ensure price scale shows by nudging autoscale after switching
        chart.applyOptions({ rightPriceScale: { autoScale: true } });
        setTimeout(()=>chart.applyOptions({ rightPriceScale: { autoScale: false } }), 0);
      }
    }

    console.log(`Chart type switched to: ${type}`);
  } catch (error) {
    console.error('Error switching chart type:', error);
  }
}

// ----- Demo Data Generation (removed) -----
/* function generateDemoData(symbol, timeframe) {
  console.log(`Generating demo data for ${symbol} (${timeframe})`);

  // Generate new demo data
  const data = [];
  const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30 days ago
  let p = 100 + Math.random() * 50; // Random starting price

  for (let i = 0; i < 500; i++) {
    const t = t0 + i * 3600; // Hourly data
    const o = p;
    const h = o + Math.random() * 3 + 1;
    const l = o - (Math.random() * 3 + 1);
    const c = l + Math.random() * (h - l);
    p = c;
    data.push({ time: t, open: o, high: h, low: l, close: c });
  }

  // Add whitespace points for future drawing
  const lastTs = data[data.length - 1].time;
  const candleInterval = data.length > 1 ? data[1].time - data[0].time : 3600;
  const numWhiteSpaces = 200;

  for (let i = 1; i <= numWhiteSpaces; i++) {
    data.push({ time: lastTs + (candleInterval * i) });
  }

  // Store current data globally
  window.currentData = data;

  // Update chart with new data
  if (series) {
    if (currentChartType === 'line') {
      const lineData = data.filter(d => d.close).map(candle => ({
        time: candle.time,
        value: candle.close
      }));
      series.setData(lineData);
    } else {
      series.setData(data);
    }

    // Set visible range to show some future space
    const futureTime = lastTs + (candleInterval * 7);
    chart.timeScale().setVisibleRange({
      from: data[Math.max(0, data.length - numWhiteSpaces - 100)].time,
      to: futureTime
    });
  }
}
*/

// ----- Initialize -----
// Set initial symbol in input
symbolInput.value = currentSymbol;

// Set initial active timeframe button
document.querySelector(`[data-timeframe="${currentTimeframe}"]`)?.classList.add('active');

// Set initial chart type
if (currentChartType === 'candlestick') {
  candleChartBtn.classList.add('active');
} else {
  lineChartBtn.classList.add('active');
}

console.log('Symbol/Timeframe controls initialized');

// Kick off initial data load using selected exchange
loadSymbolData(currentSymbol, currentTimeframe, currentExchange);
