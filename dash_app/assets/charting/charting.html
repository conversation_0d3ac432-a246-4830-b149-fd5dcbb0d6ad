<!doctype html>
<html lang="en" class="chart-layout">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Lightweight Charts — Lines + Fib 50%</title>
<link rel="stylesheet" href="charting.css">
</head>
<body class="chart-body chart-container">
  <!-- Vertical Button Sidebar - Centered -->
  <div class="button-sidebar">
    <!-- Mode Group -->
    <div class="button-group">
      <button id="mode-normal" class="chart-btn-vertical active" data-label="Normal" title="Normal chart viewing mode">📊</button>
    </div>

    <!-- Drawing Tools Group -->
    <div class="button-group">
      <button id="mode-line" class="chart-btn-vertical" data-label="Line" title="Click to place single line">➕</button>
      <button id="mode-trendline" class="chart-btn-vertical" data-label="Trendline" title="Click and drag to draw a diagonal trendline">📈</button>
      <button id="mode-fib" class="chart-btn-vertical" data-label="Fib" title="MouseDown set P1, drag, MouseUp set P2, creates retracement and take profit levels">∿</button>
      <button id="mode-rect" class="chart-btn-vertical" data-label="Rectangle" title="Click and drag to draw a rectangle">▭</button>
    </div>

    <!-- Actions Group -->
    <div class="button-group">
      <button id="auto-fib" class="chart-btn-vertical" data-label="Auto-Fib" title="Automatically draw Fibonacci retracement from most recent swing">🤖</button>
      <button id="clear" class="chart-btn-vertical" data-label="Clear" title="Remove all lines">🧹</button>
      <button id="drawings-toggle" class="chart-btn-vertical" data-label="Drawings" title="Show/Hide Drawings Panel">📋</button>
    </div>


  </div>

  <!-- Symbol/Timeframe Bar -->
  <div class="symbol-bar">
    <!-- Symbol Search -->
    <div class="symbol-search">
      <span>🔍</span>
      <input type="text" class="symbol-input" placeholder="BTCUSDT" value="BTCUSDT" id="symbol-input">
    </div>

    <!-- Timeframe Buttons -->
    <div class="timeframe-buttons">
      <button class="timeframe-btn" data-timeframe="1m">1m</button>
      <button class="timeframe-btn" data-timeframe="3m">3m</button>
      <button class="timeframe-btn" data-timeframe="5m">5m</button>
      <button class="timeframe-btn" data-timeframe="15m">15m</button>
      <button class="timeframe-btn" data-timeframe="30m">30m</button>
      <button class="timeframe-btn active" data-timeframe="1h">1h</button>
      <button class="timeframe-btn" data-timeframe="4h">4h</button>
      <button class="timeframe-btn" data-timeframe="1d">1D</button>
      <button class="timeframe-btn" data-timeframe="1w">1W</button>
    </div>
    <!-- Exchange Selector -->
    <div class="timeframe-buttons">
      <label style="color:#c9d1d9;margin-right:8px;">Exchange</label>
      <select id="exchange-select" class="chart-type-btn" style="padding:4px 8px;">
        <option value="binance" selected>Binance (USDT-M)</option>
        <option value="bybit">Bybit (Linear)</option>
      </select>
    </div>


    <!-- Chart Type Buttons -->
    <div class="chart-type-buttons">
      <button class="chart-type-btn" id="line-chart-btn" title="Line Chart">📈</button>
      <button class="chart-type-btn active" id="candle-chart-btn" title="Candlestick Chart">📊</button>
    </div>
  </div>

  <!-- Chart Area -->
  <div class="chart-area">
    <div id="chart"></div>
    <div id="loading-overlay" class="loading-overlay">Loading…</div>
  </div>

  <!-- Bottom Trade Panels -->
  <div class="trade-panels-container">
    <!-- Trade Setups Panel (Left) -->
    <div class="trade-setups-panel">
      <div class="panel-header">
        <strong>Trade Setups</strong>
      </div>
      <div id="trade-setups-list" class="trade-setups-list">
        <div class="empty-state">
          <div class="muted">No trade setups yet</div>
          <div class="instruction">Draw a Fibonacci retracement, then click "Play Trade" to add it here</div>
        </div>
      </div>
    </div>

    <!-- Trade Details Panel (Right) -->
    <div class="trade-details-panel">
      <div class="panel-header">
        <strong>Trade Details</strong>
      </div>
      <div class="trade-details-content">
        <div id="trade-info" class="muted">Draw a Fibonacci retracement to generate trade plan</div>
        <div id="play-trade-container" class="play-trade-container" style="display:none;">
          <button id="play-trade-btn" class="play-trade-btn">Play Trade</button>
        </div>
      </div>
    </div>
  </div>

  <div id="drawings-panel" class="drawings-panel">
    <strong>Drawings</strong>
    <ul id="list" class="chart-list"></ul>
  </div>

  <!-- Runtime API configuration injected before symbol-timeframe.js -->
  <script>
    // Local Chart API base (serves Binance locally and Bybit via Japan proxy)
    window.CHART_API_BASE = window.CHART_API_BASE || "http://localhost:8900";
    window.CHART_API_TOKEN = window.CHART_API_TOKEN || ""; // not needed for local chart API
  </script>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script src="chart-init.js"></script>
  <script src="line-management.js"></script>
  <script src="drawing-modes.js"></script>
  <script src="fibonacci.js"></script>
  <script src="trade-details.js"></script>
  <script src="trade-setups.js"></script>
  <script src="symbol-timeframe.js"></script>
  <script src="line-dragging-trendlines.js"></script>
  <script src="charting.js"></script>
  <script src="market-analysis.js"></script>
</body>
</html>
