// Trade Setups Management
// Handles adding, displaying, and managing trade setups created from chart drawings

let tradeSetups = [];
const API_BASE_TRADE = window.CHART_API_BASE || '';

// Initialize trade setups functionality
function initTradeSetups() {
  const playTradeBtn = document.getElementById('play-trade-btn');
  if (playTradeBtn) {
    playTradeBtn.addEventListener('click', handlePlayTrade);
  }
}

// Show/hide the Play Trade button based on whether trade details are available
function togglePlayTradeButton(show) {
  const container = document.getElementById('play-trade-container');
  if (container) {
    container.style.display = show ? 'block' : 'none';
  }
}

async function postFibonacciTrade(payload){
  try {
    const resp = await fetch(`${API_BASE_TRADE}/trades/fibonacci`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    if (!resp.ok){
      const txt = await resp.text().catch(()=> '');
      throw new Error(`HTTP ${resp.status} ${resp.statusText}${txt ? ` - ${txt.substring(0,200)}`:''}`);
    }
    return await resp.json();
  } catch (err){
    console.error('Failed to POST /trades/fibonacci', err);
    // surface a small banner in the UI
    const listEl = document.getElementById('trade-setups-list');
    if (listEl){
      const div = document.createElement('div');
      div.className = 'empty-state';
      div.textContent = `Error sending trade to API: ${err.message || err}`;
      listEl.prepend(div);
      setTimeout(()=> div.remove(), 4000);
    }
    throw err;
  }
}

// Handle Play Trade button click
async function handlePlayTrade() {
  const tradeInfo = document.getElementById('trade-info');
  if (!tradeInfo || !window.currentFibDetails) {
    console.warn('No trade details available to play');
    return;
  }

  // Build payload from current fib details and current symbol/exchange/timeframe
  const payload = {
    exchange: window.currentExchange || 'binance',
    symbol: window.currentSymbol || 'UNKNOWN',
    timeframe: window.currentTimeframe || '1h',
    p1: window.currentFibDetails.p1,
    p2: window.currentFibDetails.p2,
    fib50: window.currentFibDetails.fib50,
    fib618: window.currentFibDetails.fib618,
    fib786: window.currentFibDetails.fib786,
    fibtp236: window.currentFibDetails.fibtp236,
    fibtp382: window.currentFibDetails.fibtp382,
    timestamp: Date.now()
  };

  // Send to FastAPI
  let apiResult = null;
  try {
    apiResult = await postFibonacciTrade(payload);
    console.log('Trade created via API:', apiResult);
  } catch (_) {}

  // Create local trade setup from current fib details
  const setup = {
    id: (apiResult && apiResult.id) || crypto?.randomUUID?.() || Math.random().toString(36).slice(2),
    symbol: payload.symbol,
    exchange: payload.exchange,
    type: 'Fibonacci',
    timestamp: payload.timestamp,
    details: { ...window.currentFibDetails }
  };

  // Add to setups list
  tradeSetups.push(setup);

  // Update UI
  renderTradeSetups();

  // Hide play button after adding
  togglePlayTradeButton(false);

  console.log('Trade setup added:', setup);
}

// Render all trade setups in the list
function renderTradeSetups() {
  const listEl = document.getElementById('trade-setups-list');
  if (!listEl) return;

  if (tradeSetups.length === 0) {
    listEl.innerHTML = `
      <div class="empty-state">
        <div class="muted">No trade setups yet</div>
        <div class="instruction">Draw a Fibonacci retracement, then click "Play Trade" to add it here</div>
      </div>
    `;
    return;
  }

  listEl.innerHTML = tradeSetups.map(setup => `
    <div class="trade-setup-item" data-setup-id="${setup.id}">
      <div class="trade-setup-header">
        <div>
          <span class="trade-setup-symbol">${setup.symbol}</span>
          <span class="trade-setup-type">${setup.type}</span>
        </div>
        <button class="trade-setup-remove" onclick="removeTradeSetup('${setup.id}')">×</button>
      </div>
      <div class="trade-setup-details">
        ${formatTradeSetupDetails(setup)}
      </div>
    </div>
  `).join('');
}

// Format trade setup details for display
function formatTradeSetupDetails(setup) {
  if (setup.type === 'Fibonacci' && setup.details) {
    const d = setup.details;
    return `
      P1: ${d.p1?.toFixed(2) || 'N/A'} → P2: ${d.p2?.toFixed(2) || 'N/A'}<br>
      Entries: ${d.fib50?.toFixed(2) || 'N/A'}, ${d.fib618?.toFixed(2) || 'N/A'}, ${d.fib786?.toFixed(2) || 'N/A'}<br>
      TPs: ${d.fibtp236?.toFixed(2) || 'N/A'}, ${d.fibtp382?.toFixed(2) || 'N/A'}
    `;
  }
  return 'Details not available';
}

// Remove a trade setup
function removeTradeSetup(setupId) {
  tradeSetups = tradeSetups.filter(setup => setup.id !== setupId);
  renderTradeSetups();
  console.log('Trade setup removed:', setupId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initTradeSetups);

// Export functions for use by other modules
window.togglePlayTradeButton = togglePlayTradeButton;
window.removeTradeSetup = removeTradeSetup;
